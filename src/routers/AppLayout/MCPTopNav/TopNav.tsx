import {homeLinkCss, TopNavigation} from '@baidu/devops-components';
import styled from '@emotion/styled';
import {HomeOutlined} from '@ant-design/icons';
import {Breadcrumb} from 'antd';
import {useRouteBreadcrumbItems, useRouteDocumentTitle} from '@panda-design/router';
import {marginLeft} from '@panda-design/components';
import {useDocumentTitle} from 'huse';
import {DevTools} from '@/design/Dev/DevTools';
import {useSpaceDetail} from '@/hooks/ievalue/settings';
import {routes} from '@/routers/comatestack';
import {MCPSquareLink} from '@/links/mcp';
import {CreateMCPSpaceButton} from '@/components/MCP/CreateMCPSpaceButton';
import McpHelpDropdown from '@/components/MCP/McpHelpDropdown';

const Container = styled.div`
    display: flex;
    align-items: center;
    height: 100%;
    flex: 1;
    font-size: 14px;
`;

const Right = styled.div`
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 8px;
    height: 47px;
`;

const useDocumentContext = () => {
    const spaceDetail = useSpaceDetail();
    return {spaceName: spaceDetail?.name};
};

const TopNav = () => {
    const documentContext = useDocumentContext();
    const documentTitle = useRouteDocumentTitle(routes, documentContext);
    useDocumentTitle(documentTitle ? `MCP: ${documentTitle}` : 'MCP');
    const breadcrumbItems = useRouteBreadcrumbItems(routes);

    return (
        <Container>
            <TopNavigation
                renderHomeLink={() => (
                    <>
                        <MCPSquareLink className={homeLinkCss}>
                            <HomeOutlined />
                            <span className={marginLeft(4)}>AI Tools</span>
                        </MCPSquareLink>
                        <DevTools />
                    </>
                )}
                renderBreadcrumb={() => <Breadcrumb className={marginLeft(10)} items={breadcrumbItems} />}
                renderCreateButton={() => (
                    <Right>
                        <CreateMCPSpaceButton type="primary" />
                    </Right>
                )}
                renderActionButtonGroups={McpHelpDropdown}
            />
        </Container>
    );
};

export default TopNav;
