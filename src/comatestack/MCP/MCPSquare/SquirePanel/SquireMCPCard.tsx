/* eslint-disable max-lines */
import {Flex, Typo<PERSON>, Tooltip, Divider} from 'antd';
import styled from '@emotion/styled';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {css} from '@emotion/css';
import {But<PERSON>} from '@panda-design/components';
import {MCPDetailLink, MCPPlaygroundLink} from '@/links/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiPostViewCount} from '@/api/mcp';
import SvgEye from '@/icons/mcp/Eye';
import SvgCase from '@/icons/mcp/Case';
import SvgComment from '@/icons/mcp/Comment';
import TagGroup from '@/components/MCP/TagGroup';
import <PERSON><PERSON><PERSON> from '@/design/MCP/MCPCard';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import PublishInfo from '@/components/MCP/PublishInfo';
import {myColors} from '@/constants/colors';
import ServerTypeTag from './ServerTypeTag';

const containerCss = css`
    padding: 16px 20px 12px;
    position: relative;
    transition: all 0.3s ease;

    &:hover {
        .hover-actions {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

const HoverActions = styled.div`
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e8e8e8;
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 12px 20px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const DescriptionContainer = styled.div`
    margin: 15px 20px 13px;
    height: 36px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.4;
    text-overflow: ellipsis;
    word-break: break-word;
`;

const StatItem = styled.div`
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    color: #8f8f8f;
    font-size: 12px;

    &:hover {
        color: #317ff5;
    }

    svg {
        width: 14px;
        height: 14px;
    }
`;

const RatingText = styled.span<{score?: number}>`
    font-size: 12px;
    color: ${props => {
        if (!props.score) {
            return '#8f8f8f';
        }
        if (props.score < 4) {
            return myColors.ratingYellow;
        }
        if (props.score < 4.5) {
            return myColors.ratingOrange;
        }
        return myColors.ratingRed;
    }};
`;

// 数量格式化函数
const formatCount = (count: number): string => {
    if (count >= 10000) {
        return `${Math.floor(count / 10000)}w+`;
    }
    if (count >= 1000) {
        return `${Math.floor(count / 1000)}k+`;
    }
    return count.toString();
};

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}
const SquireMCPCard = ({server, refresh}: Props) => {
    const {
        id,
        workspaceId,
        name,
        departmentName,
        serverSourceType,
        serverProtocolType,
        description,
        labels,
        icon,
        viewCount,
        favorite,
        publishTime,
        publishUser,
    } = server;
    const navigate = useNavigate();

    const handleClick = useCallback(
        async () => {
            await apiPostViewCount({mcpServerId: id});
            navigate(MCPDetailLink.toUrl({mcpId: id}));
        },
        [navigate, id]
    );

    // 点击浏览量跳转到概览页面
    const handleViewCountClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            navigate(MCPDetailLink.toUrl({mcpId: id, tab: 'overview'}));
        },
        [navigate, id]
    );

    // 点击案例数跳转到案例页面（功能预留）
    const handleCaseClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            // TODO: 跳转到案例页面
        },
        []
    );

    // 点击评论数跳转到交流页面（功能预留）
    const handleCommentClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            // TODO: 跳转到交流页面
        },
        []
    );

    // 去MCP Playground使用
    const handlePlaygroundClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            window.open(MCPPlaygroundLink.toUrl({serverId: id}), '_blank');
        },
        [id]
    );

    // 模拟评分数据（实际应该从server对象获取）
    const rating: number | undefined = undefined; // server.rating
    const caseCount = 0; // 暂无数据
    const commentCount = 0; // 暂无数据

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={icon} />
                <Flex vertical justify="space-between" style={{overflow: 'hidden', flex: 1}} gap={4}>
                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>
                    <Flex justify="space-between" align="center" style={{width: '100%'}}>
                        <Typography.Text style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}>
                            标准MCP | {serverProtocolType}
                        </Typography.Text>
                        <RatingText score={rating}>
                            {rating ? `${rating}分` : '暂无评分'}
                        </RatingText>
                    </Flex>
                </Flex>
            </Flex>

            {/* 描述区域 */}
            <Tooltip title={description} placement="top">
                <DescriptionContainer>
                    {description || '暂无描述'}
                </DescriptionContainer>
            </Tooltip>

            {/* 部门名称 */}
            <Typography.Text style={{color: '#8f8f8f', fontSize: 12, marginBottom: 12}}>
                {departmentName || '暂无部门信息'}
            </Typography.Text>

            {/* 标签列表 */}
            <TagGroup
                labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                color="light-purple"
                gap={4}
            />
            <Divider style={{margin: '16px 0 8px'}} />
            <Flex justify="space-between" align="center" >
                <Flex align="center" gap={12}>
                    <StatItem onClick={handleViewCountClick}>
                        <SvgEye />
                        {formatCount(viewCount)}
                    </StatItem>
                    <StatItem onClick={handleCaseClick}>
                        <SvgCase />
                        {formatCount(caseCount)}
                    </StatItem>
                    <StatItem onClick={handleCommentClick}>
                        <SvgComment />
                        {formatCount(commentCount)}
                    </StatItem>
                </Flex>
                <PublishInfo username={publishUser} time={publishTime} />
            </Flex>
            <HoverActions className="hover-actions">
                <Flex justify="center" gap={12}>
                    <MCPCollectButton
                        refresh={refresh}
                        favorite={favorite}
                        serverId={id}
                        size="small"
                    />
                    <MCPSubscribeButton
                        refresh={refresh}
                        showText
                        workspaceId={workspaceId}
                        id={id}
                        size="small"
                    />
                    <Button type="primary" size="small" onClick={handlePlaygroundClick}>
                        去MCP Playground使用
                    </Button>
                </Flex>
            </HoverActions>
        </MCPCard>
    );
};

export default SquireMCPCard;
